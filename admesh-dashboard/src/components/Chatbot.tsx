"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MessageCircle, 
  X, 
  Send, 
  Bot, 
  User, 
  Mail,
  Minimize2,
  Maximize2
} from "lucide-react";
import { toast } from "sonner";

interface ChatbotAction {
  label: string;
  action: string;
  data?: Record<string, unknown>;
}

interface Message {
  id: string;
  type: "user" | "bot";
  content: string;
  timestamp: Date;
  actions?: ChatbotAction[];
}

interface ChatbotProps {
  className?: string;
}

export default function Chatbot({ }: ChatbotProps) {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [userEmail, setUserEmail] = useState("");
  const [showEmailForm, setShowEmailForm] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const sessionId = useRef(Date.now().toString());

  // Only show chatbot on landing pages
  const isLandingPage = pathname === "/" || pathname === "/brands" || pathname === "/agents" || pathname === "/users";

  // Get current page context
  const getPageContext = () => {
    if (pathname === "/brands") return "brands";
    if (pathname === "/agents") return "agents";
    if (pathname === "/users") return "users";
    return "home";
  };

  const pageContext = getPageContext();

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  // Track events function
  const trackEvent = useCallback(async (event: string, properties: Record<string, unknown> = {}) => {
    try {
      await fetch("/api/chatbot/analytics", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          event,
          properties: {
            ...properties,
            page: pathname,
            context: pageContext,
            session_id: sessionId.current
          }
        }),
      });
    } catch (error) {
      console.error("Error tracking event:", error);
    }
  }, [pathname, pageContext]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage = getWelcomeMessage(pageContext);
      setMessages([welcomeMessage]);

      // Track chatbot open
      trackEvent("chatbot_opened", {
        first_time: true
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, pageContext, messages.length]);

  // Track chatbot close
  useEffect(() => {
    if (!isOpen && messages.length > 0) {
      trackEvent("chatbot_closed", {
        messages_count: messages.length,
        session_duration: Date.now() - parseInt(sessionId.current)
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, messages.length]);

  const getWelcomeMessage = (context: string): Message => {
    const contextMessages = {
      brands: {
        content: "👋 Hi! I'm here to help you understand how AdMesh can connect your brand with high-intent customers through AI agents. What would you like to know?",
        actions: [
          { label: "How does AdMesh work?", action: "explain_brands" },
          { label: "Pricing & Pay Per Conversion", action: "pricing_brands" },
          { label: "Getting started", action: "getting_started_brands" },
          { label: "Schedule a demo", action: "schedule_demo" }
        ]
      },
      agents: {
        content: "🤖 Welcome! I can help you learn about the AdMesh Agent Pioneer Program and how you can earn by connecting users with relevant products. What interests you?",
        actions: [
          { label: "How do agents earn?", action: "explain_agents" },
          { label: "Pioneer Program benefits", action: "pioneer_program" },
          { label: "Getting started as agent", action: "getting_started_agents" },
          { label: "Join the program", action: "join_program" }
        ]
      },
      users: {
        content: "✨ Hi there! I'm here to help you understand how AdMesh enhances your AI interactions with personalized product recommendations. How can I assist you?",
        actions: [
          { label: "How does it work for users?", action: "explain_users" },
          { label: "Privacy & data", action: "privacy_users" },
          { label: "Getting started", action: "getting_started_users" },
          { label: "Sign up now", action: "sign_up" }
        ]
      },
      home: {
        content: "👋 Welcome to AdMesh! I'm here to help you understand how our platform connects brands, agents, and users through AI-powered recommendations. What brings you here today?",
        actions: [
          { label: "I'm a brand", action: "switch_to_brands" },
          { label: "I'm an AI agent", action: "switch_to_agents" },
          { label: "I'm a user", action: "switch_to_users" },
          { label: "Learn more about AdMesh", action: "explain_platform" }
        ]
      }
    };

    return {
      id: Date.now().toString(),
      type: "bot",
      content: contextMessages[context as keyof typeof contextMessages].content,
      timestamp: new Date(),
      actions: contextMessages[context as keyof typeof contextMessages].actions
    };
  };



  const addMessage = (content: string, type: "user" | "bot", actions?: Message["actions"]) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date(),
      actions
    };
    setMessages(prev => [...prev, newMessage]);

    // Track message events
    trackEvent("chatbot_message", {
      message_type: type,
      message_length: content.length,
      has_actions: actions && actions.length > 0,
      actions_count: actions?.length || 0
    });
  };

  const simulateTyping = async (duration = 1000) => {
    setIsTyping(true);
    await new Promise(resolve => setTimeout(resolve, duration));
    setIsTyping(false);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    addMessage(inputValue, "user");
    const userMessage = inputValue;
    setInputValue("");

    await simulateTyping();
    
    // Simple keyword-based responses
    const response = generateResponse(userMessage, pageContext);
    addMessage(response.content, "bot", response.actions);
  };

  const handleActionClick = async (action: string, data?: Record<string, unknown>) => {
    // Track action click
    trackEvent("chatbot_action_click", {
      action,
      data
    });

    await simulateTyping(500);

    const response = handleAction(action, data || {}, pageContext);
    addMessage(response.content, "bot", response.actions);
  };

  const generateResponse = (message: string, context: string) => {
    const lowerMessage = message.toLowerCase();

    // Pricing and cost questions
    if (lowerMessage.includes("price") || lowerMessage.includes("cost") || lowerMessage.includes("pricing")) {
      return {
        content: "AdMesh uses a Pay Per Conversion model - you only pay when we drive actual results! Brands set their own conversion values and budgets. Most brands see 3-5x ROI within the first month. Would you like to schedule a demo to discuss pricing for your specific needs?",
        actions: [
          { label: "Schedule demo", action: "schedule_demo" },
          { label: "Learn more about pricing", action: "pricing_details" },
          { label: "Get started", action: "get_started" }
        ]
      };
    }

    // How it works questions
    if (lowerMessage.includes("how") && lowerMessage.includes("work")) {
      if (context === "brands") {
        return {
          content: "AdMesh connects your products with high-intent users through AI agents. When users ask AI assistants for recommendations, our agents can suggest your products if they're relevant. You only pay when someone actually converts! It's like having a sales team working 24/7 across all AI platforms.",
          actions: [
            { label: "See integration examples", action: "integration_examples" },
            { label: "Schedule demo", action: "schedule_demo" },
            { label: "Pricing details", action: "pricing_brands" }
          ]
        };
      } else if (context === "agents") {
        return {
          content: "As an AI agent, you earn commissions by connecting users with relevant products. When someone asks for recommendations, you can suggest products from our brand partners. You earn 60% of the conversion value when someone makes a purchase through your recommendation!",
          actions: [
            { label: "Join Pioneer Program", action: "join_program" },
            { label: "How much can I earn?", action: "earnings_info" },
            { label: "Getting started", action: "getting_started_agents" }
          ]
        };
      }
    }

    // Integration and technical questions
    if (lowerMessage.includes("integrat") || lowerMessage.includes("technical") || lowerMessage.includes("api")) {
      return {
        content: "AdMesh offers simple integration options! We provide SDKs for Python, TypeScript, and a UI SDK for React. Most integrations take less than 30 minutes to set up. We also offer white-glove setup assistance for enterprise clients.",
        actions: [
          { label: "View technical docs", action: "technical_docs" },
          { label: "Schedule technical demo", action: "schedule_demo" },
          { label: "SDK examples", action: "sdk_examples" }
        ]
      };
    }

    // Getting started questions
    if (lowerMessage.includes("start") || lowerMessage.includes("begin") || lowerMessage.includes("sign up")) {
      return {
        content: "Getting started with AdMesh is easy! You can sign up for free and start testing within minutes. We offer onboarding support and a $50 credit to get you started. Would you like to create an account or schedule a demo first?",
        actions: [
          { label: "Sign up now", action: "sign_up" },
          { label: "Schedule demo", action: "schedule_demo" },
          { label: "Learn about free trial", action: "free_trial_info" }
        ]
      };
    }

    // ROI and results questions
    if (lowerMessage.includes("roi") || lowerMessage.includes("result") || lowerMessage.includes("performance")) {
      return {
        content: "Our brands typically see 3-5x ROI within the first month! AdMesh's AI-powered targeting ensures your products are shown to users with high purchase intent. We provide detailed analytics and conversion tracking so you can see exactly how your campaigns are performing.",
        actions: [
          { label: "See case studies", action: "case_studies" },
          { label: "Schedule demo", action: "schedule_demo" },
          { label: "Analytics overview", action: "analytics_info" }
        ]
      };
    }

    // Competition and comparison questions
    if (lowerMessage.includes("vs") || lowerMessage.includes("compared") || lowerMessage.includes("different") || lowerMessage.includes("competitor")) {
      return {
        content: "Unlike traditional advertising platforms, AdMesh places your products directly in AI conversations at the moment of intent. While Google Ads shows ads on search results, we integrate into the actual AI responses users see. This leads to higher conversion rates and better user experience.",
        actions: [
          { label: "See comparison chart", action: "comparison_chart" },
          { label: "Schedule demo", action: "schedule_demo" },
          { label: "Learn unique benefits", action: "unique_benefits" }
        ]
      };
    }

    // Default response
    return {
      content: "That's a great question! I'd love to connect you with our team for a personalized discussion. Would you like to schedule a quick 30-minute call or share your email for more information?",
      actions: [
        { label: "Schedule call", action: "schedule_demo" },
        { label: "Share email", action: "collect_email" },
        { label: "Browse FAQ", action: "show_faq" }
      ]
    };
  };

  const handleAction = (action: string, _data: Record<string, unknown>, context: string) => {
    switch (action) {
      case "schedule_demo":
        // Track demo scheduling intent
        trackEvent("chatbot_demo_scheduled", {
          action: "calendly_opened",
          conversion_intent: "high"
        });

        // Open Calendly in new tab
        window.open("https://calendly.com/gounimanikumar12/30min", "_blank");
        return {
          content: "Perfect! I've opened our Calendly link in a new tab. You can schedule a 30-minute call with our team to discuss how AdMesh can help your business. Is there anything else I can help you with while you're here?",
          actions: [
            { label: "Learn about pricing", action: "pricing_details" },
            { label: "Technical questions", action: "technical_info" },
            { label: "Share my email", action: "collect_email" }
          ]
        };
      
      case "collect_email":
        setShowEmailForm(true);
        return {
          content: "Great! Please share your email address and I'll make sure our team follows up with you with relevant information and updates.",
          actions: []
        };
      
      case "explain_brands":
        return {
          content: "AdMesh helps brands reach customers at the moment of intent - when they're asking AI assistants for product recommendations. Our AI agents can suggest your products in natural conversations, and you only pay for actual conversions. It's like having a sales team working 24/7 across all AI platforms!",
          actions: [
            { label: "How much does it cost?", action: "pricing_brands" },
            { label: "See integration examples", action: "integration_examples" },
            { label: "Schedule demo", action: "schedule_demo" }
          ]
        };
      
      case "pricing_brands":
        return {
          content: "Our Pay Per Conversion model means you only pay for results! You set your conversion value (e.g., $5 for a lead, $50 for a sale) and we optimize to drive those conversions. No upfront costs, no monthly fees - just results-based pricing. Most brands see 3-5x ROI within the first month.",
          actions: [
            { label: "Schedule pricing call", action: "schedule_demo" },
            { label: "Get started now", action: "get_started" },
            { label: "Technical integration", action: "technical_info" }
          ]
        };

      case "explain_agents":
        return {
          content: "AI agents in our network earn 60% of conversion values when they successfully connect users with relevant products. For example, if you recommend a $100 product and someone buys it, you earn $60! The more relevant your recommendations, the more you earn.",
          actions: [
            { label: "Join Pioneer Program", action: "join_program" },
            { label: "See earning examples", action: "earnings_examples" },
            { label: "How to get started", action: "getting_started_agents" }
          ]
        };

      case "explain_users":
        return {
          content: "AdMesh enhances your AI interactions by providing personalized product recommendations when you ask for suggestions. Instead of generic responses, you get curated options from trusted brands. It's completely free for users and helps you discover products that actually match your needs!",
          actions: [
            { label: "How is it free?", action: "free_for_users" },
            { label: "Privacy & data", action: "privacy_users" },
            { label: "Sign up now", action: "sign_up" }
          ]
        };

      case "integration_examples":
        return {
          content: "AdMesh integrates seamlessly with popular AI platforms! For example, when someone asks ChatGPT 'What's the best project management tool?', our agents can recommend your product if it's relevant. We support integration with custom AI assistants, chatbots, and recommendation engines.",
          actions: [
            { label: "View technical docs", action: "technical_docs" },
            { label: "See live examples", action: "live_examples" },
            { label: "Schedule technical demo", action: "schedule_demo" }
          ]
        };

      case "get_started":
      case "sign_up":
        // Track signup intent
        trackEvent("chatbot_signup_intent", {
          action: "signup_page_opened",
          conversion_intent: "high",
          role: context === "agents" ? "agent" : "brand"
        });

        // Redirect to sign up page
        const signupRole = context === "agents" ? "agent" : "brand";
        window.open(`/auth/signin?role=${signupRole}`, "_blank");
        return {
          content: "Perfect! I've opened the sign-up page in a new tab. You can create your free account and start testing AdMesh right away. We'll give you $50 in credits to get started! Need any help with the setup process?",
          actions: [
            { label: "Schedule onboarding call", action: "schedule_demo" },
            { label: "Technical questions", action: "technical_info" },
            { label: "Share my email for updates", action: "collect_email" }
          ]
        };

      case "join_program":
        // Redirect to agent sign up
        window.open("/auth/signin?role=agent", "_blank");
        return {
          content: "Excellent! I've opened the Agent Pioneer Program signup in a new tab. Join now to be among the first agents in our network and get access to exclusive benefits and higher commission rates!",
          actions: [
            { label: "Learn about benefits", action: "pioneer_benefits" },
            { label: "Earnings calculator", action: "earnings_calculator" },
            { label: "Questions about program", action: "collect_email" }
          ]
        };

      case "technical_docs":
        window.open("https://docs.useadmesh.com", "_blank");
        return {
          content: "I've opened our technical documentation in a new tab! You'll find comprehensive guides for our Python SDK, TypeScript SDK, and UI components. Our docs include code examples, API references, and integration tutorials.",
          actions: [
            { label: "Schedule technical demo", action: "schedule_demo" },
            { label: "Need integration help?", action: "collect_email" },
            { label: "View SDK examples", action: "sdk_examples" }
          ]
        };

      case "show_faq":
        return {
          content: "Here are some frequently asked questions:\n\n• How much does AdMesh cost? - Pay per conversion only!\n• How long does integration take? - Usually under 30 minutes\n• What's the minimum commitment? - No minimum, cancel anytime\n• Do you support my platform? - We support most AI platforms\n\nWhat specific question can I help you with?",
          actions: [
            { label: "Pricing details", action: "pricing_details" },
            { label: "Integration help", action: "technical_info" },
            { label: "Schedule demo", action: "schedule_demo" },
            { label: "Contact support", action: "collect_email" }
          ]
        };

      default:
        return {
          content: "I'd love to help you with that! Let me connect you with our team for detailed information. Would you prefer to schedule a call or share your email?",
          actions: [
            { label: "Schedule call", action: "schedule_demo" },
            { label: "Share email", action: "collect_email" }
          ]
        };
    }
  };

  const handleEmailSubmit = async () => {
    if (!userEmail || !userEmail.includes("@")) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      // Send email <NAME_EMAIL>
      const response = await fetch("/api/chatbot/lead", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: userEmail,
          page: pathname,
          context: pageContext,
          timestamp: new Date().toISOString(),
        }),
      });

      if (response.ok) {
        toast.success("Thank you! We'll be in touch soon.");
        setShowEmailForm(false);
        setUserEmail("");

        // Track successful email submission
        trackEvent("chatbot_email_submitted", {
          email: userEmail,
          conversion: true
        });

        addMessage(
          "Perfect! I've shared your email with our team. You'll hear from us within 24 hours with relevant information and next steps. Thanks for your interest in AdMesh!",
          "bot",
          [
            { label: "Schedule call now", action: "schedule_demo" },
            { label: "Browse more info", action: "show_faq" }
          ]
        );
      } else {
        throw new Error("Failed to submit");
      }
    } catch {
      toast.error("Something went wrong. Please try again or email us <NAME_EMAIL>");
    }
  };

  // Don't render anything if not on a landing page
  if (!isLandingPage) {
    return null;
  }

  return (
    <>
      {/* Chatbot Toggle Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            className="fixed bottom-4 right-4 z-[70] sm:bottom-6 sm:right-6"
          >
            <Button
              onClick={() => setIsOpen(true)}
              className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-primary hover:bg-primary/90"
              size="icon"
            >
              <MessageCircle className="h-6 w-6" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chatbot Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0, y: 100 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0, opacity: 0, y: 100 }}
            className="fixed bottom-0 right-0 z-[70] w-full h-full sm:bottom-4 sm:right-4 sm:w-96 sm:max-w-[calc(100vw-1rem)] sm:h-[600px] sm:max-h-[calc(100vh-1rem)] md:bottom-6 md:right-6 md:max-w-[calc(100vw-2rem)] md:max-h-[calc(100vh-2rem)]"
          >
            <Card className="h-full flex flex-col shadow-2xl border-2 sm:rounded-lg rounded-none overflow-hidden">
              {/* Header */}
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 bg-primary text-primary-foreground rounded-t-lg">
                <div className="flex items-center space-x-2">
                  <Bot className="h-5 w-5" />
                  <CardTitle className="text-lg white">AdMesh Assistant</CardTitle>
                  <Badge variant="secondary" className="text-xs">
                    Beta
                  </Badge>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20"
                    onClick={() => setIsMinimized(!isMinimized)}
                  >
                    {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20"
                    onClick={() => setIsOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>

              {/* Messages Area */}
              <AnimatePresence>
                {!isMinimized && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="flex-1 flex flex-col min-h-0"
                  >
                    <CardContent className="flex-1 overflow-y-auto p-4 space-y-4 min-h-0" style={{ scrollbarWidth: 'thin' }}>
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${message.type === "user" ? "justify-end" : "justify-start"} flex-shrink-0`}
                        >
                          <div
                            className={`max-w-[80%] rounded-lg p-3 ${
                              message.type === "user"
                                ? "bg-primary text-primary-foreground"
                                : "bg-muted"
                            }`}
                          >
                            <div className="flex items-start space-x-2">
                              {message.type === "bot" && <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm break-words">{message.content}</p>
                                {message.actions && message.actions.length > 0 && (
                                  <div className="mt-3 space-y-2">
                                    {message.actions.map((action, index) => (
                                      <Button
                                        key={index}
                                        variant="outline"
                                        size="sm"
                                        className="w-full text-left justify-start h-auto py-2 px-3 text-xs break-words"
                                        onClick={() => handleActionClick(action.action, action.data)}
                                      >
                                        <span className="truncate">{action.label}</span>
                                      </Button>
                                    ))}
                                  </div>
                                )}
                              </div>
                              {message.type === "user" && <User className="h-4 w-4 mt-0.5 flex-shrink-0" />}
                            </div>
                          </div>
                        </div>
                      ))}
                      
                      {isTyping && (
                        <div className="flex justify-start">
                          <div className="bg-muted rounded-lg p-3">
                            <div className="flex items-center space-x-2">
                              <Bot className="h-4 w-4" />
                              <div className="flex space-x-1">
                                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      <div ref={messagesEndRef} />
                    </CardContent>

                    {/* Email Form */}
                    {showEmailForm && (
                      <div className="p-4 border-t bg-muted/30">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm font-medium">Share your email</span>
                          </div>
                          <div className="flex space-x-2">
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              value={userEmail}
                              onChange={(e) => setUserEmail(e.target.value)}
                              onKeyPress={(e) => e.key === "Enter" && handleEmailSubmit()}
                              className="flex-1"
                            />
                            <Button onClick={handleEmailSubmit} size="sm">
                              <Send className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Input Area */}
                    {!showEmailForm && (
                      <div className="p-4 border-t">
                        <div className="flex space-x-2">
                          <Input
                            placeholder="Type your message..."
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                            className="flex-1"
                          />
                          <Button onClick={handleSendMessage} size="sm" disabled={!inputValue.trim()}>
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-2 text-center">
                          Powered by AdMesh AI • <a href="mailto:<EMAIL>" className="hover:underline">Need help?</a>
                        </p>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
