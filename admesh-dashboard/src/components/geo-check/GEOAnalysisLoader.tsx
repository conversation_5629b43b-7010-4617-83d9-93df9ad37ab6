import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Search,
  Globe,
  Brain,
  BarChart3,
  Lightbulb,
  RefreshCw,
  CheckCircle,
  Clock
} from "lucide-react";
import { useEffect, useState } from "react";

interface AnalysisStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  estimatedTime: number; // in seconds
  status: "pending" | "active" | "completed";
}

interface GEOAnalysisLoaderProps {
  isLoading: boolean;
  brandName?: string;
  website?: string;
}

export function GEOAnalysisLoader({ isLoading, brandName, website }: GEOAnalysisLoaderProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [steps, setSteps] = useState<AnalysisStep[]>([
    {
      id: "discovery",
      title: "Discovering Website Pages",
      description: "Scanning your website&apos;s sitemap and identifying key pages for the report",
      icon: <Search className="h-4 w-4" />,
      estimatedTime: 15,
      status: "pending"
    },
    {
      id: "content-analysis",
      title: "Analyzing Content Quality",
      description: "Evaluating page structure, factual claims, and AI-friendliness using advanced LLM processing",
      icon: <Globe className="h-4 w-4" />,
      estimatedTime: 25,
      status: "pending"
    },
    {
      id: "ai-visibility",
      title: "Simulating AI Visibility",
      description: "Testing how your brand appears in AI-generated responses across multiple queries",
      icon: <Brain className="h-4 w-4" />,
      estimatedTime: 20,
      status: "pending"
    },
    {
      id: "scoring",
      title: "Calculating GEO Scores",
      description: "Computing weighted scores for prompt mentions, citations, and optimization metrics",
      icon: <BarChart3 className="h-4 w-4" />,
      estimatedTime: 10,
      status: "pending"
    },
    {
      id: "recommendations",
      title: "Generating Recommendations",
      description: "Creating personalized insights and actionable recommendations for improvement",
      icon: <Lightbulb className="h-4 w-4" />,
      estimatedTime: 10,
      status: "pending"
    }
  ]);

  const totalEstimatedTime = steps.reduce((sum, step) => sum + step.estimatedTime, 0);

  useEffect(() => {
    if (!isLoading) {
      setCurrentStepIndex(0);
      setElapsedTime(0);
      setSteps(prev => prev.map(step => ({ ...step, status: "pending" })));
      return;
    }

    const interval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [isLoading]);

  useEffect(() => {
    if (!isLoading) return;

    let cumulativeTime = 0;
    let newCurrentStepIndex = 0;

    for (let i = 0; i < steps.length; i++) {
      if (elapsedTime >= cumulativeTime && elapsedTime < cumulativeTime + steps[i].estimatedTime) {
        newCurrentStepIndex = i;
        break;
      }
      cumulativeTime += steps[i].estimatedTime;
      if (elapsedTime >= cumulativeTime) {
        newCurrentStepIndex = i + 1;
      }
    }

    if (newCurrentStepIndex !== currentStepIndex) {
      setCurrentStepIndex(newCurrentStepIndex);

      setSteps(prev => prev.map((step, index) => ({
        ...step,
        status: index < newCurrentStepIndex ? "completed" :
                index === newCurrentStepIndex ? "active" : "pending"
      })));
    }
  }, [elapsedTime, isLoading, currentStepIndex, steps]);

  const progressPercentage = Math.min((elapsedTime / totalEstimatedTime) * 100, 100);
  const remainingTime = Math.max(totalEstimatedTime - elapsedTime, 0);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  if (!isLoading) return null;

  return (
    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <RefreshCw className="h-6 w-6 text-blue-600 animate-spin" />
            </div>
            <div>
              <CardTitle className="text-lg text-blue-700 dark:text-blue-300">
                Analyzing {brandName || "Your Brand"}&apos;s GEO Performance
              </CardTitle>
              <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                Running comprehensive report on {website || "your website"}
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
              <Clock className="h-4 w-4" />
              <span>{formatTime(remainingTime)} remaining</span>
            </div>
            <div className="text-xs text-blue-500 dark:text-blue-500 mt-1">
              {Math.round(progressPercentage)}% complete
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium text-blue-700 dark:text-blue-300">Overall Progress</span>
            <span className="text-blue-600 dark:text-blue-400">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} className="h-3" />
        </div>

        {/* Analysis Steps */}
        <div className="space-y-3">
          <h4 className="font-medium text-blue-700 dark:text-blue-300 text-sm">Analysis Steps</h4>
          <div className="space-y-3">
            {steps.map((step, index) => (
              <div 
                key={step.id}
                className={`flex items-start gap-3 p-3 rounded-lg transition-all duration-300 ${
                  step.status === "active" 
                    ? "bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800" 
                    : step.status === "completed"
                    ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"
                    : "bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
                }`}
              >
                <div className={`flex-shrink-0 p-1.5 rounded-full ${
                  step.status === "active" 
                    ? "bg-blue-200 dark:bg-blue-800 text-blue-700 dark:text-blue-300" 
                    : step.status === "completed"
                    ? "bg-green-200 dark:bg-green-800 text-green-700 dark:text-green-300"
                    : "bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                }`}>
                  {step.status === "completed" ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : step.status === "active" ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    step.icon
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h5 className={`font-medium text-sm ${
                      step.status === "active" 
                        ? "text-blue-700 dark:text-blue-300" 
                        : step.status === "completed"
                        ? "text-green-700 dark:text-green-300"
                        : "text-gray-600 dark:text-gray-400"
                    }`}>
                      {step.title}
                    </h5>
                    <Badge 
                      variant={
                        step.status === "active" ? "default" : 
                        step.status === "completed" ? "secondary" : "outline"
                      }
                      className="text-xs"
                    >
                      {step.status === "active" ? "In Progress" : 
                       step.status === "completed" ? "Complete" : "Pending"}
                    </Badge>
                  </div>
                  <p className={`text-xs ${
                    step.status === "active" 
                      ? "text-blue-600 dark:text-blue-400" 
                      : step.status === "completed"
                      ? "text-green-600 dark:text-green-400"
                      : "text-gray-500 dark:text-gray-500"
                  }`}>
                    {step.description}
                  </p>
                  {step.status === "active" && (
                    <div className="mt-2">
                      <Progress 
                        value={((elapsedTime - steps.slice(0, index).reduce((sum, s) => sum + s.estimatedTime, 0)) / step.estimatedTime) * 100} 
                        className="h-1" 
                      />
                    </div>
                  )}
                </div>
                
                <div className={`text-xs ${
                  step.status === "active" 
                    ? "text-blue-600 dark:text-blue-400" 
                    : step.status === "completed"
                    ? "text-green-600 dark:text-green-400"
                    : "text-gray-500 dark:text-gray-500"
                }`}>
                  {formatTime(step.estimatedTime)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Current Step Details */}
        {currentStepIndex < steps.length && (
          <div className="bg-blue-100 dark:bg-blue-900/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1 bg-blue-200 dark:bg-blue-800 rounded-full">
                {steps[currentStepIndex]?.icon}
              </div>
              <h4 className="font-medium text-blue-700 dark:text-blue-300 text-sm">
                Currently: {steps[currentStepIndex]?.title}
              </h4>
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-400 mb-3">
              {steps[currentStepIndex]?.description}
            </p>

            {/* Step-specific insights */}
            <div className="text-xs text-blue-700 dark:text-blue-300">
              {currentStepIndex === 0 && (
                <p><strong>Why this matters:</strong> We identify your most important pages to ensure comprehensive report coverage.</p>
              )}
              {currentStepIndex === 1 && (
                <p><strong>Why this matters:</strong> AI models prefer well-structured, factual content with clear headings and evidence-based claims.</p>
              )}
              {currentStepIndex === 2 && (
                <p><strong>Why this matters:</strong> This shows how often your brand appears in AI responses to industry-related queries.</p>
              )}
              {currentStepIndex === 3 && (
                <p><strong>Why this matters:</strong> We calculate weighted scores based on prompt mentions (40%), citations (20%), content quality (30%), and sentiment (10%).</p>
              )}
              {currentStepIndex === 4 && (
                <p><strong>Why this matters:</strong> Personalized recommendations help you improve your GEO performance and AI visibility.</p>
              )}
            </div>
          </div>
        )}

        {/* Additional Info */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-xs text-blue-700 dark:text-blue-300">
            <strong>About GEO Report:</strong> Unlike traditional SEO, GEO focuses on optimizing for AI-powered search engines.
            We analyze how your brand appears in AI responses, content structure, and provide actionable insights for the AI-first future.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
