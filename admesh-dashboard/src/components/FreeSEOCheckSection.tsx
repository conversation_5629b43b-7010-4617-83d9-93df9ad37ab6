"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useAuth } from "@/hooks/use-auth";
import {
  Brain,
  Search,
  Target,
  TrendingUp,
  <PERSON>rkles,
  ArrowRight,
  BarChart3
} from "lucide-react";

export default function FreeSEOCheckSection() {
  const { user } = useAuth();
  const { ref: sectionRef, inView: sectionInView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const handleGEOReport = () => {
    if (user) {
      // User is logged in, redirect to GEO report page
      window.location.href = "/dashboard/brand/geo-check";
    } else {
      // User is not logged in, redirect to sign-in with brand role
      window.location.href = "/auth/signin?role=brand&redirect=/dashboard/brand/geo-check";
    }
  };

  const geoFeatures = [
    {
      icon: <Brain className="w-6 h-6 text-black dark:text-white" />,
      title: "AI Citation Analysis",
      description: "Track AI engine citations and mentions"
    },
    {
      icon: <Search className="w-6 h-6 text-black dark:text-white" />,
      title: "Content Optimization",
      description: "AI-friendly content scoring and insights"
    },
    {
      icon: <Target className="w-6 h-6 text-black dark:text-white" />,
      title: "Competitive Analysis",
      description: "Compare against competitor AI visibility"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-black dark:text-white" />,
      title: "GEO Recommendations",
      description: "Actionable steps to improve AI rankings"
    }
  ];



  return (
    <section
      id="free-geo-report"
      ref={sectionRef}
      className="w-full py-20 bg-gray-50 dark:bg-gray-900"
    >
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <span className="inline-flex items-center gap-2 mb-4 px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-full text-sm font-medium">
            <Sparkles className="w-4 h-4" />
            Free GEO Report
          </span>
          <h2 className="text-3xl md:text-4xl font-bold text-black dark:text-white mb-6">
            Free GEO Report
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            See how your website performs in AI search engines and get actionable recommendations.
          </p>
        </motion.div>

        {/* Why GEO Matters Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Why GEO Matters
            </h3>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              AI engines like ChatGPT and Claude are changing how users find information.
              GEO optimizes your content for AI citations, not just search rankings.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {geoFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: sectionInView ? 1 : 0,
                  y: sectionInView ? 0 : 20,
                }}
                transition={{ duration: 0.5, delay: 0.4 + (index * 0.1) }}
                className="text-center p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md dark:hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{feature.title}</h4>
                <p className="text-sm text-gray-600 dark:text-gray-300">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{
            opacity: sectionInView ? 1 : 0,
            y: sectionInView ? 0 : 30,
          }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center"
        >
          <Button
            onClick={handleGEOReport}
            size="lg"
            className="bg-black dark:bg-white hover:bg-gray-800 dark:hover:bg-gray-100 text-white dark:text-black font-medium shadow-md hover:shadow-lg transition-all hover:-translate-y-1 duration-300"
          >
            <div className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              {user ? "Start GEO Report" : "Get Free GEO Report"}
              <ArrowRight className="w-5 h-5" />
            </div>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
