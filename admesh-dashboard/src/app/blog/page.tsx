import { client, blogPostsQuery, featuredPostsQuery, BlogPost, urlFor, getCategoryColor } from '@/lib/sanity'
import Link from 'next/link'
import { format } from 'date-fns'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BlogImage } from '@/components/blog/BlogImage'
import { CalendarDays, User, ArrowRight, Clock, Star } from 'lucide-react'

async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    const posts = await client.fetch(blogPostsQuery, {}, {
      next: {
        revalidate: 60, // Revalidate every 60 seconds
        tags: ['blog-posts']
      }
    })
    return posts || []
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}

async function getFeaturedPosts(): Promise<BlogPost[]> {
  try {
    const posts = await client.fetch(featuredPostsQuery, {}, {
      next: {
        revalidate: 60, // Revalidate every 60 seconds
        tags: ['blog-posts']
      }
    })
    return posts || []
  } catch (error) {
    console.error('Error fetching featured posts:', error)
    return []
  }
}

export default async function BlogPage() {
  const [posts, featuredPosts] = await Promise.all([
    getBlogPosts(),
    getFeaturedPosts()
  ])

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-24">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-10 tracking-tight">
            AdMesh Blog
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Insights, updates, and stories from the AdMesh ecosystem. Discover the latest in AI marketing,
            performance optimization, and industry trends.
          </p>
        </div>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <div className="mb-24">
            <div className="flex items-center justify-center gap-3 mb-16">
              <Star className="h-6 w-6 text-yellow-500" />
              <h2 className="text-3xl md:text-4xl font-bold text-foreground">Featured Posts</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredPosts.map((post) => (
                <Card key={post._id} className="group hover:shadow-xl transition-all duration-300 border border-border bg-gradient-to-br from-background to-muted/10 hover:border-primary/30 overflow-hidden relative">
                  <CardHeader className="p-0">
                    {post.mainImage ? (
                      <div className="relative aspect-[16/10] w-full overflow-hidden bg-muted">
                        <BlogImage
                          src={urlFor(post.mainImage).width(500).height(312).url()}
                          alt={post.mainImage.alt || post.title}
                          fill
                          className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                        <div className="absolute top-4 left-4">
                          <Badge className="bg-yellow-500 text-yellow-900 hover:bg-yellow-600 shadow-lg">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <div className="aspect-[16/10] w-full bg-muted flex items-center justify-center">
                        <div className="text-muted-foreground text-sm">No image</div>
                      </div>
                    )}
                  </CardHeader>
                  <CardContent className="p-7">
                    <div className="space-y-5 h-full flex flex-col">
                      {/* Categories */}
                      {post.categories && post.categories.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {post.categories.slice(0, 2).map((category) => (
                            <Badge
                              key={category._id}
                              variant="secondary"
                              className={`${getCategoryColor(category.color)} px-3 py-1`}
                            >
                              {category.title}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Title */}
                      <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2 leading-tight">
                        {post.title}
                      </h3>

                      {/* Excerpt */}
                      {post.excerpt && (
                        <p className="text-muted-foreground line-clamp-3 leading-relaxed flex-grow">
                          {post.excerpt}
                        </p>
                      )}

                      {/* Meta Information */}
                      <div className="flex items-center justify-between text-sm text-muted-foreground pt-4 border-t border-border mt-auto">
                        <div className="flex items-center gap-4">
                          {post.author && (
                            <div className="flex items-center gap-2">
                              {post.author.image && (
                                <Avatar className="h-6 w-6">
                                  <AvatarImage
                                    src={urlFor(post.author.image).width(24).height(24).url()}
                                    alt={post.author.name}
                                  />
                                  <AvatarFallback className="text-xs">
                                    {post.author.name.charAt(0)}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                              <span className="font-medium">{post.author.name}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <CalendarDays className="h-4 w-4" />
                            <span>{format(new Date(post.publishedAt), 'MMM dd')}</span>
                          </div>
                        </div>
                        {(post.readingTime || post.estimatedReadingTime) && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{post.readingTime || post.estimatedReadingTime} min</span>
                          </div>
                        )}
                      </div>

                      {/* Read More Link */}
                      <Link
                        href={`/blog/${post.slug.current}`}
                        className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors pt-2 relative z-20"
                      >
                        Read more
                        <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </CardContent>

                  {/* Clickable overlay */}
                  <Link
                    href={`/blog/${post.slug.current}`}
                    className="absolute inset-0 z-10"
                    aria-label={`Read ${post.title}`}
                  />
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* All Blog Posts */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-12">
            {featuredPosts.length > 0 ? 'Latest Posts' : 'All Posts'}
          </h2>
        </div>

        {posts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {posts.filter(post => !post.featured).map((post) => (
              <Card key={post._id} className="group hover:shadow-lg transition-all duration-300 border border-border overflow-hidden relative">
                <CardHeader className="p-0">
                  {post.mainImage ? (
                    <div className="relative aspect-[16/10] w-full overflow-hidden bg-muted">
                      <BlogImage
                        src={urlFor(post.mainImage).width(500).height(312).url()}
                        alt={post.mainImage.alt || post.title}
                        fill
                        className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                    </div>
                  ) : (
                    <div className="aspect-[16/10] w-full bg-muted flex items-center justify-center">
                      <div className="text-muted-foreground text-sm">No image</div>
                    </div>
                  )}
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4 h-full flex flex-col">
                    {/* Categories */}
                    {post.categories && post.categories.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {post.categories.slice(0, 2).map((category) => (
                          <Badge
                            key={category._id}
                            variant="secondary"
                            className={`${getCategoryColor(category.color)} px-3 py-1`}
                          >
                            {category.title}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Title */}
                    <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2 leading-tight">
                      {post.title}
                    </h3>

                    {/* Excerpt */}
                    {/* {post.excerpt && (
                      <p className="text-muted-foreground line-clamp-3 leading-relaxed flex-grow">
                        {post.excerpt}
                      </p>
                    )} */}

                    {/* Meta Information */}
                    <div className="flex items-center justify-between text-sm text-muted-foreground pt-4 border-t border-border mt-auto">
                      <div className="flex items-center gap-4">
                        {post.author && (
                          <div className="flex items-center gap-2">
                            {post.author.image && (
                              <Avatar className="h-6 w-6">
                                <AvatarImage
                                  src={urlFor(post.author.image).width(24).height(24).url()}
                                  alt={post.author.name}
                                />
                                <AvatarFallback className="text-xs">
                                  {post.author.name.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                            )}
                            <span className="font-medium">{post.author.name}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <CalendarDays className="h-4 w-4" />
                          <span>{format(new Date(post.publishedAt), 'MMM dd')}</span>
                        </div>
                      </div>
                      {(post.readingTime || post.estimatedReadingTime) && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>{post.readingTime || post.estimatedReadingTime} min</span>
                        </div>
                      )}
                    </div>

                    {/* Read More Link */}
                    <Link
                      href={`/blog/${post.slug.current}`}
                      className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors pt-2 relative z-20"
                    >
                      Read more
                      <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </div>
                </CardContent>

                {/* Clickable overlay */}
                <Link
                  href={`/blog/${post.slug.current}`}
                  className="absolute inset-0 z-10"
                  aria-label={`Read ${post.title}`}
                />
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="max-w-lg mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
                <User className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-3xl font-semibold text-foreground mb-4">
                No blog posts yet
              </h3>
              <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
                We're working on creating amazing content about AI marketing, performance optimization,
                and industry insights. Check back soon for the latest updates!
              </p>
              <Badge variant="secondary" className="px-6 py-3 text-sm">
                Coming Soon
              </Badge>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Generate metadata for SEO
export async function generateMetadata() {
  return {
    title: 'Blog | AdMesh - AI Marketing Insights & Updates',
    description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem. Learn about AI marketing, performance optimization, and industry trends.',
    keywords: ['AI marketing', 'performance marketing', 'AdMesh blog', 'marketing insights', 'AI agents'],
    openGraph: {
      title: 'Blog | AdMesh - AI Marketing Insights & Updates',
      description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem. Learn about AI marketing, performance optimization, and industry trends.',
      type: 'website',
      siteName: 'AdMesh',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'Blog | AdMesh - AI Marketing Insights & Updates',
      description: 'Discover the latest insights, updates, and stories from the AdMesh ecosystem.',
    },
  }
}
